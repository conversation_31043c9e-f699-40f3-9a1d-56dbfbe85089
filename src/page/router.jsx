import { Navigate, Route, Routes } from "react-router-dom";
import BaseLayout from "widgets/layouts/BaseLayout";
import { HomePage } from "./home";
import SanctionsRouter from "./sanctions/SanctionsRouter";
import WhiteListRouter from "./whiteList/WhiteListRouter";
import FoundRouter from "./found/FoundRouter";
import Loggers from "features/loggers/Loggers";

const Router = () => {
    return <BaseLayout>
        <Routes>
            <Route path="/" element={<HomePage />} />
            <Route path="/sanctions/*" element={<SanctionsRouter />} />
            <Route path="/white-list/*" element={<WhiteListRouter />} />
            <Route path="/login/*" element={<FoundRouter />} />
            <Route path="/loggi/*" element={<Loggers />} />
            <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
    </BaseLayout>
}
export default Router;          