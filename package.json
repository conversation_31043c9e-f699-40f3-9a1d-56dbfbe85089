{"name": "gateway-tunduk-front", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@heroicons/react": "^2.1.3", "@mui/icons-material": "^5.15.21", "@mui/material": "^5.15.21", "@mui/system": "^5.15.20", "@reduxjs/toolkit": "^2.2.5", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.13", "axios": "^1.7.2", "face-api.js": "^0.22.2", "i": "^0.3.7", "npm": "^10.8.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-google-recaptcha": "^3.1.0", "react-hook-form": "^7.51.5", "react-redux": "^9.1.2", "react-router-dom": "^6.23.1", "react-toastify": "^10.0.5", "react-webcam": "^7.2.0", "react18-json-view": "^0.2.8", "versoly-ui": "^2.1.1", "vite-jsconfig-paths": "^2.0.1"}, "devDependencies": {"@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.19", "eslint": "^8.57.0", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "postcss": "^8.4.38", "tailwindcss": "^3.4.3", "vite": "^5.2.0"}}