import { configureStore } from '@reduxjs/toolkit';
import authSlice from './reducer/auth.reducer';
import sanctionsSlice from './reducer/sanctions.reducer';
import whiteListSlice from './reducer/whiteList.reducer';
import foundSlice from './reducer/found.reducer';
import loggersSlice from './reducer/loggers.reducer';

export const store = configureStore({
    reducer: {
        auth: authSlice.reducer,
        sanctions: sanctionsSlice.reducer,
        whiteList: whiteListSlice.reducer,
        found: foundSlice.reducer,
        loggers: loggersSlice.reducer
    },
});
